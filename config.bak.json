{"$schema": "https://schema.freqtrade.io/schema.json", "strategy": "AODualSideStrategy", "timeframe": "5m", "max_open_trades": 1, "stake_currency": "USDC", "stake_amount": "unlimited", "tradable_balance_ratio": 0.97, "fiat_display_currency": "", "dry_run": false, "dry_run_wallet": 1000, "cancel_open_orders_on_exit": true, "trading_mode": "futures", "margin_mode": "isolated", "use_exit_signal": true, "exit_profit_only": false, "ignore_roi_if_entry_signal": true, "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "hyperliquid", "walletAddress": "******************************************", "privateKey": "0x1c0dc4657737f314a1bcacdc5cf0d72adb80340c97268cabc8a55a3dee1d3f0a", "ccxt_config": {}, "ccxt_async_config": {}, "type": "perpetual", "leverage": 5, "pair_whitelist": ["ETH/USDC:USDC"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": true, "token": "**********************************************", "chat_id": "118585485"}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "59db6b3f0dbf560e6532284901f250c8738984a85e5da2b6f3354ecc01abce42", "ws_token": "tVfFTvkK8r00NiDVoANp_IPPcY8C-GjSEA", "CORS_origins": [], "username": "freqtrader", "password": ""}, "bot_name": "freqtrade", "initial_state": "running", "force_entry_enable": true, "internals": {"process_throttle_secs": 5}}