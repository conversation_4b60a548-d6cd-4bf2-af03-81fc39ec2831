from freqtrade.strategy import IStrategy
from datetime import datetime
import pandas as pd
from typing import Optional

class AODualSideStrategy(IStrategy):
    INTERFACE_VERSION = 3

    timeframe = '3m'
    max_open_trades = 1

    order_types = {
        'entry': 'market',
        'exit': 'market',
        'emergency_exit': 'market',
        'force_exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    minimal_roi = {
        "0": 3.0  # 300% TP
    }
    stoploss = -0.8
    startup_candle_count: int = 1

    can_short = True
    position_adjustment_enable = False

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None, side: str,
                 **kwargs) -> float:
        if side == 'long':
            return 2  # Leverage for long positions
        elif side == 'short':
            return 1  # Leverage for short positions
        else:
            return 1  # Default leverage

    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        median_price = (dataframe['high'] + dataframe['low']) / 2
        dataframe['ao_fast'] = median_price.rolling(window=5).mean()
        dataframe['ao_slow'] = median_price.rolling(window=34).mean()
        dataframe['ao'] = dataframe['ao_fast'] - dataframe['ao_slow']
        return dataframe

    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0

        # AO crosses above 0 → Enter LONG
        dataframe.loc[
            (dataframe['ao'].shift(1) < 0) &
            (dataframe['ao'] > 0),
            ['enter_long', 'enter_tag']
        ] = (1, 'ao_cross_up')

        # AO crosses below 0 → Enter SHORT
        dataframe.loc[
            (dataframe['ao'].shift(1) > 0) &
            (dataframe['ao'] < 0),
            ['enter_short', 'enter_tag']
        ] = (1, 'ao_cross_down')

        return dataframe

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        # Exit LONG when AO turns negative
        dataframe.loc[
            (dataframe['ao'].shift(1) > 0) &
            (dataframe['ao'] < 0),
            ['exit_long', 'exit_tag']
        ] = (1, 'ao_cross_down_exit')

        # Exit SHORT when AO turns positive
        dataframe.loc[
            (dataframe['ao'].shift(1) < 0) &
            (dataframe['ao'] > 0),
            ['exit_short', 'exit_tag']
        ] = (1, 'ao_cross_up_exit')

        return dataframe
